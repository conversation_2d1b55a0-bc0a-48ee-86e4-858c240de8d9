{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npx tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@descope/react-sdk": "^2.14.26", "@headlessui/react": "^2.2.4", "@playwright/test": "^1.54.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "clsx": "^2.1.1", "dotenv": "^17.2.0", "framer-motion": "^12.23.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.6.3", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}, "devDependencies": {"@descope/node-sdk": "^1.7.8", "@eslint/js": "^9.29.0", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}