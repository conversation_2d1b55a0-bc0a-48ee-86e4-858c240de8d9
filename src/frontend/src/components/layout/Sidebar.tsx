import { useLocation } from 'react-router';
import {
  Sidebar as CatalystSidebar,
  SidebarBody,
  SidebarHeader,
  SidebarItem,
  SidebarLabel,
  SidebarSection
} from '../ui/sidebar';
import { ROUTES } from '../../routes/routes';
import {
  DashboardIcon,
  WorkflowIcon,
  OpportunityIcon,
  SettingsIcon
} from '../ui/icons';

/**
 * Navigation items configuration with modern icons
 */
const navigationItems = [
  {
    name: 'Dashboard',
    href: ROUTES.DASHBOARD,
    icon: <DashboardIcon className="w-5 h-5" />,
    description: 'Overview and analytics',
  },
  {
    name: 'Workflows',
    href: ROUTES.WORKFLOWS,
    icon: <WorkflowIcon className="w-5 h-5" />,
    description: 'Automated processes',
  },
  {
    name: 'Opportunities',
    href: ROUTES.OPPORTUNITIES,
    icon: <OpportunityIcon className="w-5 h-5" />,
    description: 'Sales pipeline',
  },
];

/**
 * Sidebar navigation component
 * Uses Catalyst Sidebar components for consistent styling and behavior
 */
export function Sidebar() {
  const location = useLocation();

  const isCurrentPath = (href: string) => {
    if (href === ROUTES.DASHBOARD) {
      return location.pathname === href;
    }
    return location.pathname.startsWith(href);
  };

  return (
    <CatalystSidebar>
      <SidebarHeader>
        <div className="flex items-center gap-3">
          {/* Enhanced logo with gradient background */}
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-secondary-500 shadow-soft">
            <span className="text-sm font-bold text-white">IL</span>
          </div>
          <div className="flex flex-col">
            <SidebarLabel className="text-lg font-bold tracking-tight">Shevet</SidebarLabel>
            <span className="text-xs text-zinc-500 dark:text-zinc-400">Workflow Platform</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarBody>
        {/* Main Navigation */}
        <SidebarSection>
          <div className="space-y-1">
            {navigationItems.map((item) => (
              <SidebarItem
                key={item.name}
                href={item.href}
                current={isCurrentPath(item.href)}
                className="group relative"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <SidebarLabel className="font-medium">{item.name}</SidebarLabel>
                    <p className="text-xs text-zinc-500 dark:text-zinc-400 group-hover:text-zinc-600 dark:group-hover:text-zinc-300 transition-colors">
                      {item.description}
                    </p>
                  </div>
                </div>
              </SidebarItem>
            ))}
          </div>
        </SidebarSection>

        {/* Secondary Navigation */}
        <SidebarSection className="mt-8 border-t border-zinc-200 pt-6 dark:border-zinc-700">
          <SidebarItem
            href={ROUTES.SETTINGS}
            current={isCurrentPath(ROUTES.SETTINGS)}
            className="group"
          >
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <SettingsIcon className="w-5 h-5" />
              </div>
              <div className="flex-1 min-w-0">
                <SidebarLabel className="font-medium">Settings</SidebarLabel>
                <p className="text-xs text-zinc-500 dark:text-zinc-400 group-hover:text-zinc-600 dark:group-hover:text-zinc-300 transition-colors">
                  Preferences & config
                </p>
              </div>
            </div>
          </SidebarItem>
        </SidebarSection>
      </SidebarBody>
    </CatalystSidebar>
  );
}
