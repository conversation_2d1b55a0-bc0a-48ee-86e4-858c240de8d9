import React from 'react';
import { Button } from './button';

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    color?: 'indigo' | 'green' | 'blue' | 'purple';
  };
  className?: string;
}

/**
 * Empty state component for when there's no data to display
 * Provides consistent styling and optional call-to-action
 */
export function EmptyState({
  icon,
  title,
  description,
  action,
  className,
}: EmptyStateProps) {
  const defaultIcon = (
    <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
      />
    </svg>
  );

  return (
    <div className={`text-center py-12 ${className || ''}`}>
      <div className="mx-auto mb-4">
        {icon || defaultIcon}
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-gray-600 mb-6 max-w-sm mx-auto">{description}</p>
      )}
      {action && (
        <Button 
          color={action.color || 'indigo'} 
          onClick={action.onClick}
        >
          {action.label}
        </Button>
      )}
    </div>
  );
}

/**
 * Specific empty states for common scenarios
 */
export function NoWorkflowsState({ onCreateWorkflow }: { onCreateWorkflow: () => void }) {
  return (
    <EmptyState
      icon={
        <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      }
      title="No workflows yet"
      description="Get started by creating your first automated workflow to streamline your processes."
      action={{
        label: 'Create Workflow',
        onClick: onCreateWorkflow,
        color: 'indigo',
      }}
    />
  );
}

export function NoOpportunitiesState({ onCreateOpportunity }: { onCreateOpportunity: () => void }) {
  return (
    <EmptyState
      icon={
        <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      }
      title="No opportunities yet"
      description="Start tracking your sales opportunities to better manage your pipeline and close more deals."
      action={{
        label: 'Create Opportunity',
        onClick: onCreateOpportunity,
        color: 'green',
      }}
    />
  );
}

export function NoSearchResultsState({ searchTerm, onClearSearch }: { searchTerm: string; onClearSearch: () => void }) {
  return (
    <EmptyState
      icon={
        <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      }
      title="No results found"
      description={`No results found for "${searchTerm}". Try adjusting your search terms or filters.`}
      action={{
        label: 'Clear Search',
        onClick: onClearSearch,
        color: 'indigo',
      }}
    />
  );
}
