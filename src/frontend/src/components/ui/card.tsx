import clsx from 'clsx'
import React from 'react'

type CardProps = {
  className?: string
  children: React.ReactNode
  hover?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'soft' | 'medium' | 'large'
} & React.ComponentPropsWithoutRef<'div'>

const paddingStyles = {
  none: '',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
}

const shadowStyles = {
  none: '',
  soft: 'shadow-soft',
  medium: 'shadow-medium',
  large: 'shadow-large',
}

export function Card({ 
  className, 
  children, 
  hover = false,
  padding = 'md',
  shadow = 'soft',
  ...props 
}: CardProps) {
  return (
    <div
      {...props}
      className={clsx(
        className,
        // Base styles
        'relative rounded-xl border bg-white dark:bg-zinc-900',
        // Border
        'border-zinc-200 dark:border-zinc-700',
        // Padding
        paddingStyles[padding],
        // Shadow
        shadowStyles[shadow],
        // Hover effects
        hover && [
          'transition-all duration-200 ease-out',
          'hover:shadow-medium hover:-translate-y-0.5',
          'hover:border-zinc-300 dark:hover:border-zinc-600',
        ]
      )}
    >
      {children}
    </div>
  )
}

export function CardHeader({ 
  className, 
  children, 
  ...props 
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      {...props}
      className={clsx(
        className,
        'border-b border-zinc-200 px-6 py-4 dark:border-zinc-700'
      )}
    >
      {children}
    </div>
  )
}

export function CardContent({ 
  className, 
  children, 
  ...props 
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      {...props}
      className={clsx(className, 'p-6')}
    >
      {children}
    </div>
  )
}

export function CardFooter({ 
  className, 
  children, 
  ...props 
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      {...props}
      className={clsx(
        className,
        'border-t border-zinc-200 px-6 py-4 dark:border-zinc-700'
      )}
    >
      {children}
    </div>
  )
}

// Specialized card components for common use cases
export function StatsCard({
  title,
  value,
  icon,
  trend,
  trendValue,
  color = 'primary',
  className,
  ...props
}: {
  title: string
  value: string | number
  icon?: React.ReactNode
  trend?: 'up' | 'down' | 'neutral'
  trendValue?: string
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  className?: string
} & Omit<CardProps, 'children'>) {
  const colorStyles = {
    primary: 'bg-primary-500 text-white',
    secondary: 'bg-secondary-500 text-white',
    success: 'bg-success-500 text-white',
    warning: 'bg-warning-500 text-white',
    error: 'bg-error-500 text-white',
  }

  const trendStyles = {
    up: 'text-success-600 dark:text-success-400',
    down: 'text-error-600 dark:text-error-400',
    neutral: 'text-zinc-500 dark:text-zinc-400',
  }

  return (
    <Card className={className} hover {...props}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-zinc-600 dark:text-zinc-400">
            {title}
          </p>
          <p className="mt-2 text-3xl font-bold tracking-tight text-zinc-900 dark:text-white">
            {value}
          </p>
          {trend && trendValue && (
            <p className={clsx('mt-2 text-sm font-medium', trendStyles[trend])}>
              {trend === 'up' && '↗'} {trend === 'down' && '↘'} {trendValue}
            </p>
          )}
        </div>
        {icon && (
          <div className={clsx(
            'flex h-12 w-12 items-center justify-center rounded-xl',
            colorStyles[color]
          )}>
            {icon}
          </div>
        )}
      </div>
    </Card>
  )
}
