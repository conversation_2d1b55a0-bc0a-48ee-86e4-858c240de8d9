import clsx from 'clsx'

type HeadingProps = { level?: 1 | 2 | 3 | 4 | 5 | 6 } & React.ComponentPropsWithoutRef<
  'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
>

const headingStyles = {
  1: 'text-4xl/tight font-bold tracking-tight text-zinc-950 sm:text-3xl/tight dark:text-white',
  2: 'text-3xl/tight font-bold tracking-tight text-zinc-950 sm:text-2xl/tight dark:text-white',
  3: 'text-2xl/tight font-semibold tracking-tight text-zinc-950 sm:text-xl/tight dark:text-white',
  4: 'text-xl/tight font-semibold tracking-tight text-zinc-950 sm:text-lg/tight dark:text-white',
  5: 'text-lg/tight font-semibold text-zinc-950 sm:text-base/tight dark:text-white',
  6: 'text-base/tight font-semibold text-zinc-950 sm:text-sm/tight dark:text-white',
}

export function Heading({ className, level = 1, ...props }: HeadingProps) {
  let Element: `h${typeof level}` = `h${level}`

  return (
    <Element
      {...props}
      className={clsx(className, headingStyles[level])}
    />
  )
}

export function Subheading({ className, level = 2, ...props }: HeadingProps) {
  let Element: `h${typeof level}` = `h${level}`

  return (
    <Element
      {...props}
      className={clsx(
        className,
        'text-lg/relaxed font-medium text-zinc-700 sm:text-base/relaxed dark:text-zinc-300'
      )}
    />
  )
}
