import clsx from 'clsx'
import { Link } from './link'

type TextVariant = 'body' | 'caption' | 'small' | 'large'
type TextColor = 'primary' | 'secondary' | 'tertiary' | 'success' | 'warning' | 'error'

const textVariants = {
  large: 'text-lg/relaxed sm:text-base/relaxed',
  body: 'text-base/relaxed sm:text-sm/relaxed',
  caption: 'text-sm/relaxed sm:text-xs/relaxed',
  small: 'text-xs/relaxed',
}

const textColors = {
  primary: 'text-zinc-950 dark:text-white',
  secondary: 'text-zinc-700 dark:text-zinc-300',
  tertiary: 'text-zinc-500 dark:text-zinc-400',
  success: 'text-green-700 dark:text-green-400',
  warning: 'text-amber-700 dark:text-amber-400',
  error: 'text-red-700 dark:text-red-400',
}

export function Text({
  className,
  variant = 'body',
  color = 'secondary',
  ...props
}: React.ComponentPropsWithoutRef<'p'> & {
  variant?: TextVariant
  color?: TextColor
}) {
  return (
    <p
      data-slot="text"
      {...props}
      className={clsx(className, textVariants[variant], textColors[color])}
    />
  )
}

export function TextLink({ className, ...props }: React.ComponentPropsWithoutRef<typeof Link>) {
  return (
    <Link
      {...props}
      className={clsx(
        className,
        'font-medium text-primary-600 underline decoration-primary-600/30 transition-colors hover:text-primary-700 hover:decoration-primary-700/50 dark:text-primary-400 dark:decoration-primary-400/30 dark:hover:text-primary-300 dark:hover:decoration-primary-300/50'
      )}
    />
  )
}

export function Strong({ className, ...props }: React.ComponentPropsWithoutRef<'strong'>) {
  return <strong {...props} className={clsx(className, 'font-semibold text-zinc-950 dark:text-white')} />
}

export function Code({ className, ...props }: React.ComponentPropsWithoutRef<'code'>) {
  return (
    <code
      {...props}
      className={clsx(
        className,
        'rounded-md border border-zinc-200 bg-zinc-50 px-1.5 py-0.5 text-sm font-mono text-zinc-900 dark:border-zinc-700 dark:bg-zinc-800 dark:text-zinc-100'
      )}
    />
  )
}

export function Caption({ className, ...props }: React.ComponentPropsWithoutRef<'p'>) {
  return (
    <p
      {...props}
      className={clsx(className, 'text-sm/relaxed text-zinc-500 dark:text-zinc-400')}
    />
  )
}

export function Label({ className, ...props }: React.ComponentPropsWithoutRef<'label'>) {
  return (
    <label
      {...props}
      className={clsx(className, 'text-sm font-medium text-zinc-950 dark:text-white')}
    />
  )
}
