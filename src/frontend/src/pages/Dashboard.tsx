import { Heading } from '../components/ui/heading';
import { Text } from '../components/ui/text';
import { Card, StatsCard, CardHeader, CardContent } from '../components/ui/card';
import {
  WorkflowIcon,
  OpportunityIcon,
  TaskIcon,
  RevenueIcon,
  ActivityIcon
} from '../components/ui/icons';

/**
 * Dashboard page - main landing page for authenticated users
 */
export default function Dashboard() {
  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header Section */}
      <div className="space-y-2">
        <Heading level={1}>Dashboard</Heading>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Active Workflows"
          value={12}
          icon={<WorkflowIcon />}
          color="primary"
          trend="up"
          trendValue="+12% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.1s' }}
        />

        <StatsCard
          title="Open Opportunities"
          value={24}
          icon={<OpportunityIcon />}
          color="success"
          trend="up"
          trendValue="+8% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.2s' }}
        />

        <StatsCard
          title="Pending Tasks"
          value={8}
          icon={<TaskIcon />}
          color="warning"
          trend="down"
          trendValue="-4% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.3s' }}
        />

        <StatsCard
          title="Revenue This Month"
          value="$45.2K"
          icon={<RevenueIcon />}
          color="secondary"
          trend="up"
          trendValue="+15% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.4s' }}
        />
      </div>

      {/* Recent Activity */}
      <Card
        className="animate-slide-up"
        style={{ animationDelay: '0.5s' }}
        shadow="medium"
      >
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-zinc-100 dark:bg-zinc-800">
              <ActivityIcon className="h-5 w-5 text-zinc-600 dark:text-zinc-400" />
            </div>
            <Heading level={3}>Recent Activity</Heading>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900">
                <div className="h-2 w-2 rounded-full bg-primary-600 dark:bg-primary-400"></div>
              </div>
              <div className="flex-1 space-y-1">
                <Text color="primary" variant="body">
                  New workflow "Lead Qualification" was created
                </Text>
                <Text color="tertiary" variant="caption">
                  2 hours ago
                </Text>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-success-100 dark:bg-success-900">
                <div className="h-2 w-2 rounded-full bg-success-600 dark:bg-success-400"></div>
              </div>
              <div className="flex-1 space-y-1">
                <Text color="primary" variant="body">
                  Opportunity "Enterprise Deal" moved to "Negotiation"
                </Text>
                <Text color="tertiary" variant="caption">
                  4 hours ago
                </Text>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-warning-100 dark:bg-warning-900">
                <div className="h-2 w-2 rounded-full bg-warning-600 dark:bg-warning-400"></div>
              </div>
              <div className="flex-1 space-y-1">
                <Text color="primary" variant="body">
                  Task "Follow up with client" was completed
                </Text>
                <Text color="tertiary" variant="caption">
                  6 hours ago
                </Text>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
