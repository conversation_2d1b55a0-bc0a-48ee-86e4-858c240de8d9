import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { Heading } from '../components/ui/heading';
import { Text } from '../components/ui/text';
import { Button } from '../components/ui/button';

export default function Profile() {
  const { user, logout } = useAuth();

  return (
    <div className="space-y-6">
      <div>
        <Heading level={1}>Profile</Heading>
        <Text className="mt-2 text-gray-600">
          Manage your account information
        </Text>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="space-y-4">
          <div>
            <Text className="text-sm font-medium text-gray-500">Name</Text>
            <Text className="mt-1 text-gray-900">{user?.name || 'Not provided'}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">Email</Text>
            <Text className="mt-1 text-gray-900">{user?.email || 'Not provided'}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">Role</Text>
            <Text className="mt-1 text-gray-900">{user?.role || 'user'}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">User ID</Text>
            <Text className="mt-1 text-gray-900 font-mono text-sm">{user?.id || 'Not provided'}</Text>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <Button color="red" onClick={logout}>
            Sign Out
          </Button>
        </div>
      </div>
    </div>
  );
}
