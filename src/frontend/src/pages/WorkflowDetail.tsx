import React from 'react';
import { useParams } from 'react-router-dom';
import { Heading } from '../components/ui/heading';
import { Text } from '../components/ui/text';

export default function WorkflowDetail() {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="space-y-6">
      <div>
        <Heading level={1}>Workflow Details</Heading>
        <Text className="mt-2 text-gray-600">
          Workflow ID: {id}
        </Text>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <Text className="text-gray-500">
          Workflow detail interface will be implemented in Phase 1B
        </Text>
      </div>
    </div>
  );
}
