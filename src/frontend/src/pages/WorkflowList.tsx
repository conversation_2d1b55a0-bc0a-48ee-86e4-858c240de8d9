import React from 'react';
import { Heading } from '../components/ui/heading';
import { Text } from '../components/ui/text';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import { WorkflowIcon } from '../components/ui/icons';

export default function WorkflowList() {
  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Heading level={1}>Workflows</Heading>
          <Text color="secondary" variant="large">
            Manage and monitor your automated workflows
          </Text>
        </div>
        <Button
          color="primary"
          className="inline-flex items-center space-x-2 shadow-soft hover:shadow-medium transition-shadow"
        >
          <WorkflowIcon className="h-4 w-4" />
          <span>Create Workflow</span>
        </Button>
      </div>

      {/* Coming Soon Card */}
      <Card
        className="text-center animate-slide-up"
        padding="lg"
        shadow="medium"
      >
        <div className="space-y-4">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900">
            <WorkflowIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
          </div>
          <div className="space-y-2">
            <Heading level={3}>Coming Soon</Heading>
            <Text color="secondary">
              Workflow management interface will be implemented in Phase 1B
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
}
