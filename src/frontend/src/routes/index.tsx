import React from 'react';
import App from '../App';
import { Layout } from '../components/layout/Layout';
import { Routes, Route } from "react-router";

// Lazy load pages for better performance
const Dashboard = React.lazy(() => import('../pages/Dashboard'));
const WorkflowList = React.lazy(() => import('../pages/WorkflowList'));
const WorkflowDetail = React.lazy(() => import('../pages/WorkflowDetail'));
const OpportunityList = React.lazy(() => import('../pages/OpportunityList'));
const OpportunityDetail = React.lazy(() => import('../pages/OpportunityDetail'));
const Settings = React.lazy(() => import('../pages/Settings'));

/**
 * Application routes configuration
 */
export const AppRouter = () => {
  return (
    <Routes>
      <Route path="/" element={<App />}>
        <Route element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="workflows">
            <Route index element={<WorkflowList />} />
            <Route path=":id" element={<WorkflowDetail />} />
          </Route>
          <Route path="opportunities">
            <Route index element={<OpportunityList />} />
            <Route path=":id" element={<OpportunityDetail />} />
          </Route>
          <Route path="settings" element={<Settings />} />
        </Route>
      </Route>
      {/* <Route path="*" element={<Dashboard />} /> */}
    </Routes>
  );
}