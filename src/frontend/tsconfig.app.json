{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2022",
    "useDefineForClassFields": true,
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["src", "ignore/src/e2e/app.spec.ts", "ignore/src/e2e/authenticated.spec.ts", "ignore/src/e2e/final-test.spec.ts", "ignore/src/e2e/integration.spec.ts", "ignore/src/e2e/login-flow.spec.ts", "ignore/src/e2e/manual-test.spec.ts", "ignore/src/e2e/real-login.spec.ts", "e2e"]
}
